import pandas as pd
import seaborn as sns

df = pd.read_json("sample_similarity_jsons.json")

df.head()

df["percent_node_with_valid_type"] = df["num_nodes_with_valid_type"] / df["num_nodes"] * 100
df["percent_node_with_valid_version"] = df["num_nodes_with_valid_version"] / df["num_nodes"] * 100
df["percent_node_with_valid_structure"] = df["num_nodes_with_valid_structure"] / df["num_nodes"] * 100
df["percent_connections_with_valid_structure"] = df["num_connections_with_valid_structure"] / df["num_connections"] * 100
df["percent_connections_with_valid_target_node"] = df["num_connections_with_valid_target_node"] / df["num_connections"] * 100
df["percent_node_with_parameters"] = df["num_nodes_with_parameters"] / df["num_nodes"] * 100

sns.histplot(df, x="top_level_keys_present")

sns.histplot(df, x="workflow_name_valid")

sns.histplot(df, x="percent_node_with_valid_type")

sns.histplot(df, x="percent_node_with_valid_version")

sns.histplot(df, x="percent_node_with_valid_structure")

sns.histplot(df, x="percent_node_with_parameters")

sns.histplot(df, x="percent_connections_with_valid_structure")

sns.histplot(df, x="percent_connections_with_valid_target_node")

sns.histplot(df, x="active_field_boolean")

