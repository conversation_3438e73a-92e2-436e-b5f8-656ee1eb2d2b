from openai import OpenAI
import json
requesty_api_key = "sk-PcLyYtnhSm2UntRhL6aECcw1ZGxqbDoeU8midASAgsSwAuh/Sy5cX0YF6/O/tkyvDFvdKJLrmJMFY4z7ha7obgkcsoPHiwQnO4icZabGGg4="

def load_node_types_and_versions():
    """Parse the n8n_nodes_list.txt file to extract node types and their valid versions."""
    node_types = set()
    node_versions = {}

    try:
        with open("n8n_nodes_list.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("- ") and " : " in line:
                    # Parse format: "- node_type : version1,version2,version3"
                    parts = line[2:].split(
                        " : ", 1
                    )  # Remove "- " prefix and split on " : "
                    if len(parts) == 2:
                        node_type = parts[0].strip()
                        versions_str = parts[1].strip()

                        # Parse versions (comma-separated)
                        versions = [v.strip() for v in versions_str.split(",")]

                        node_types.add(node_type)
                        node_versions[node_type] = versions

    except FileNotFoundError:
        print("Warning: n8n_nodes_list.txt not found, falling back to empty lists")

    return node_types, node_versions


VALID_N8N_NODE_TYPES, VALID_N8N_NODE_VERSIONS = load_node_types_and_versions()

def valid_node_type_and_version(n8n_json: str):
    pass


def llm_score(prompt: str, n8n_json: str):
    client = OpenAI(
        api_key=requesty_api_key,
        base_url="https://router.requesty.ai/v1",
    )
    verification_prompt = f"""Evaluate if the JSON fulfills the given PROMPT.

PROMPT: {prompt}
JSON: {n8n_json}

Score 0-10 for:
1. Prompt-JSON Match: Does JSON fulfill the prompt?

Respond only with JSON:
{{"prompt_json_score": <score>, "summary": "<brief_assessment>"}}"""

    response = client.chat.completions.create(
        model="google/gemini-2.5-flash",
        messages=[
            {
                "role": "system",
                "content": "You are an expert evaluator. Respond only with valid JSON.",
            },
            {"role": "user", "content": verification_prompt},
        ],
    )

    result_text = response.choices[0].message.content.strip()
    try:
        result_data = json.loads(result_text)
    except json.JSONDecodeError:
        if "```json" in result_text:
            json_start = result_text.find("```json") + 7
            json_end = result_text.find("```", json_start)
            result_text = result_text[json_start:json_end].strip()
            result_data = json.loads(result_text)
        else:
            raise

    return result_data.get("prompt_json_score", 0.0)
